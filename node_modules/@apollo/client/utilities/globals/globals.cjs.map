{"version": 3, "file": "globals.cjs", "sources": ["../../version.js", "maybe.js", "global.js", "../common/makeUniqueId.js", "../common/stringifyForDisplay.js", "invariantWrappers.js", "index.js"], "sourcesContent": ["export var version = \"3.12.8\";\n//# sourceMappingURL=version.js.map", "export function maybe(thunk) {\n    try {\n        return thunk();\n    }\n    catch (_a) { }\n}\n//# sourceMappingURL=maybe.js.map", "import { maybe } from \"./maybe.js\";\nexport default (maybe(function () { return globalThis; }) ||\n    maybe(function () { return window; }) ||\n    maybe(function () { return self; }) ||\n    maybe(function () { return global; }) || // We don't expect the Function constructor ever to be invoked at runtime, as\n// long as at least one of globalThis, window, self, or global is defined, so\n// we are under no obligation to make it easy for static analysis tools to\n// detect syntactic usage of the Function constructor. If you think you can\n// improve your static analysis to detect this obfuscation, think again. This\n// is an arms race you cannot win, at least not in JavaScript.\nmaybe(function () {\n    return maybe.constructor(\"return this\")();\n}));\n//# sourceMappingURL=global.js.map", "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n    var count = prefixCounts.get(prefix) || 1;\n    prefixCounts.set(prefix, count + 1);\n    return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n//# sourceMappingURL=makeUniqueId.js.map", "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"stringifyForDisplay\");\n    return JSON.stringify(value, function (key, value) {\n        return value === void 0 ? undefId : value;\n    }, space)\n        .split(JSON.stringify(undefId))\n        .join(\"<undefined>\");\n}\n//# sourceMappingURL=stringifyForDisplay.js.map", "import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\nfunction wrap(fn) {\n    return function (message) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (typeof message === \"number\") {\n            var arg0 = message;\n            message = getHandledErrorMsg(arg0);\n            if (!message) {\n                message = getFallbackErrorMsg(arg0, args);\n                args = [];\n            }\n        }\n        fn.apply(void 0, [message].concat(args));\n    };\n}\nvar invariant = Object.assign(function invariant(condition, message) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    if (!condition) {\n        originalInvariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));\n    }\n}, {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n});\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(message) {\n    var optionalParams = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        optionalParams[_i - 1] = arguments[_i];\n    }\n    return new InvariantError(getHandledErrorMsg(message, optionalParams) ||\n        getFallbackErrorMsg(message, optionalParams));\n}\nvar ApolloErrorMessageHandler = Symbol.for(\"ApolloErrorMessageHandler_\" + version);\nfunction stringify(arg) {\n    if (typeof arg == \"string\") {\n        return arg;\n    }\n    try {\n        return stringifyForDisplay(arg, 2).slice(0, 1000);\n    }\n    catch (_a) {\n        return \"<non-serializable>\";\n    }\n}\nfunction getHandledErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return (global[ApolloErrorMessageHandler] &&\n        global[ApolloErrorMessageHandler](message, messageArgs.map(stringify)));\n}\nfunction getFallbackErrorMsg(message, messageArgs) {\n    if (messageArgs === void 0) { messageArgs = []; }\n    if (!message)\n        return;\n    return \"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#\".concat(encodeURIComponent(JSON.stringify({\n        version: version,\n        message: message,\n        args: messageArgs.map(stringify),\n    })));\n}\nexport { invariant, InvariantError, newInvariantError, ApolloErrorMessageHandler, };\n//# sourceMappingURL=invariantWrappers.js.map", "import { invariant, newInvariantError, InvariantError, } from \"./invariantWrappers.js\";\nexport { maybe } from \"./maybe.js\";\nexport { default as global } from \"./global.js\";\nexport { invariant, newInvariantError, InvariantError };\n/**\n * @deprecated we do not use this internally anymore,\n * it is just exported for backwards compatibility\n */\n// this file is extempt from automatic `__DEV__` replacement\n// so we have to write it out here\n// @ts-ignore\nexport var DEV = globalThis.__DEV__ !== false;\nexport { DEV as __DEV__ };\n//# sourceMappingURL=index.js.map"], "names": ["originalInvariant", "InvariantError", "global"], "mappings": ";;;;;;AAAO,IAAI,OAAO,GAAG,QAAQ;;ACAtB,SAAS,KAAK,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI;AACR,QAAQ,OAAO,KAAK,EAAE,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,GAAG;AAClB;;ACJA,eAAe,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;AACzD,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AACzC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACvC,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AAMzC,KAAK,CAAC,YAAY;AAClB,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,CAAC,CAAC;;ACZF,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAGtB,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG;;ACNO,SAAS,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE;AAClD,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,qBAAqB,CAAC,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;AACvD,QAAQ,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;AAClD,KAAK,EAAE,KAAK,CAAC;AACb,SAAS,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACvC,SAAS,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7B;;ACLA,SAAS,IAAI,CAAC,EAAE,EAAE;AAClB,IAAI,OAAO,UAAU,OAAO,EAAE;AAC9B,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACzC,YAAY,IAAI,IAAI,GAAG,OAAO,CAAC;AAC/B,YAAY,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/C,YAAY,IAAI,CAAC,OAAO,EAAE;AAC1B,gBAAgB,OAAO,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,gBAAgB,IAAI,GAAG,EAAE,CAAC;AAC1B,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,KAAK,CAAC;AACN,CAAC;AACE,IAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACrE,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQA,qBAAiB,CAAC,SAAS,EAAE,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9G,KAAK;AACL,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,IAAI,GAAG,EAAE,IAAI,CAACA,qBAAiB,CAAC,GAAG,CAAC;AACpC,IAAI,IAAI,EAAE,IAAI,CAACA,qBAAiB,CAAC,IAAI,CAAC;AACtC,IAAI,KAAK,EAAE,IAAI,CAACA,qBAAiB,CAAC,KAAK,CAAC;AACxC,CAAC,EAAE;AAWH,SAAS,iBAAiB,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,cAAc,GAAG,EAAE,CAAC;AAC5B,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,IAAIC,0BAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE,cAAc,CAAC;AACzE,QAAQ,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;AACtD,CAAC;AACD,IAAI,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,GAAG,OAAO,CAAC,CAAC;AACnF,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,IAAI,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE;AAChC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,OAAO,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,QAAQ,OAAO,oBAAoB,CAAC;AACpC,KAAK;AACL,CAAC;AACD,SAAS,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE;AAClD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,QAAQC,QAAM,CAAC,yBAAyB,CAAC;AAC7C,QAAQA,QAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE;AAChF,CAAC;AACD,SAAS,mBAAmB,CAAC,OAAO,EAAE,WAAW,EAAE;AACnD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,CAAC,OAAO;AAChB,QAAQ,OAAO;AACf,IAAI,OAAO,8FAA8F,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC;AACnJ,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;AACxC,KAAK,CAAC,CAAC,CAAC,CAAC;AACT;;ACtEU,IAAC,GAAG,GAAG,UAAU,CAAC,OAAO,KAAK;;;;;;;;;;"}