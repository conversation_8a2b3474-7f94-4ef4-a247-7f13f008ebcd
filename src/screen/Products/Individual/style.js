import tw from "twin.macro";
import styled from "styled-components";
import { Link } from "react-router-dom";

import { HeadingTag } from "../../../partials/Heading/style";
import { SearchScreenCriteria, SearchScreenCriteriaWrapper, SearchScreenFieldInput, SearchScreenFieldInputWrapper, SearchScreenPopularText } from "../../Search/style";

export const PIContainer = styled.div`
  ${tw`flex pb-[50px] pr-[20px] my-0 mx-auto w-full min-h-[1350px] max-w-[1260px]`}
  ${tw`xxl:flex-col xxl:pb-[50px] xxl:px-[20px] pl-0`}
  @media screen and (min-width: 1301px) {
    ${tw`max-w-full`}
  }
`
export const PIWrapper = styled.div`
  ${tw`flex w-full flex-col`}
  @media screen and (min-width: 1301px) {
    ${tw`w-[calc(1009px)] my-0 mx-auto`}
  }
`

export const PINav = styled.div`
  ${tw`flex flex-col mr-[50px] border-0 border-r border-solid border-r-sidenav-border-color`}
  ${tw`xxl:static xxl:top-auto xxl:left-auto xxl:w-full xxl:pb-[30px] xxl:flex-wrap xxl:flex-row xxl:border-r-0 xxl:items-center`}
  ${tw`xl:flex-row xl:flex-nowrap xl:overflow-x-scroll xl:pb-0`}
  ${tw`sm:mr-0`}
`
export const PIProduct = styled.div`
  ${tw`flex w-full lg:flex-col`}
  @media screen and (min-width: 1301px) {
    ${tw`w-[calc(1009px)] my-0 mx-auto`}
  }
`
export const PILeftPane = styled.div`
  ${tw`flex flex-col w-[330px] pr-[30px] flex-[1_0_300px]`}
  ${tw`lg:flex-1`}
  ${tw`sm:w-full`}
`
export const PIRightPane = styled.div`
  ${tw`flex flex-col flex-[1_0_calc(100% - 330px)]`}
`
export const PIProductHeading = styled(HeadingTag)`
  ${tw`mt-[17px] mx-0 mb-[15px] pb-[10px] border-0 border-solid border-b-2 border-b-common-color`}
  span {
    ${tw`font-sans`}
  }
`
export const PIProductImage = styled.div`
  ${tw`relative w-[290px] h-[220px] flex p-[20px] items-center justify-center`}
  & > figure {
    ${tw`text-center m-0`}
    img {
      ${tw`max-h-[200px]`}
    }
  }
`
export const PIProductDimension = styled.span`
  ${tw`absolute min-w-[25px] text-center inline-block text-small`}
`
export const PIProductDescription = styled.p`
  ${tw`mt-[15px] py-0 px-[5px] font-museo-sans text-common-color text-small opacity-100`}
  transition: opacity 0.5s ease-out 0s;
  ${props => props.isHidden ? tw`opacity-0` : tw``}
`
export const PISearchContainer = styled.div`
  ${props => props.isOtherAlu ? tw`mb-[100px] ` : tw`mb-[200px] `}
  ${tw`lg:mb-[20px]`}
`
export const PINavLink = styled(Link).withConfig({
  shouldForwardProp: (props) => !['isActive'].includes(props)
})`
  ${tw`w-[140px] h-[140px] p-[10px] flex flex-col justify-center items-center text-center cursor-pointer text-paragraph-color no-underline`}
  ${tw`xxl:flex-[0_0_20%]`}
  @media screen and (max-width: 700px){
    ${tw`flex-[0_0_25%]`}
  }
  ${tw`sm:flex-[0_0_33%]`}
  @media screen and (max-width: 400px){
    ${tw`flex-[0_0_50%]`}
  }
  ${props => props.isActive ? tw`pointer-events-none bg-drawer-menu-color` : tw``}
  & > span {
    ${tw`text-small`}
  }
  img {
    ${tw`max-w-[80px]`}
  }

`
export const PINavLinkImg = styled.img`
  ${tw`mb-[10px]`}
`
export const PISearchInputWrapper = styled(SearchScreenFieldInputWrapper)`
  ${tw`w-1/2 border-b-2 border-b-common-color`}
  ${tw`lg:w-full`}
`
export const PISearchInput = styled(SearchScreenFieldInput)`
  ${tw``}
`
export const PISearchCriteriaWrapper = styled(SearchScreenCriteriaWrapper)`
  ${tw`mt-5 px-0 lg:mb-[30px] sm:flex-wrap`}
`
export const PISearchCriteria = styled(SearchScreenCriteria)`
  ${tw`px-[5px] border-r-border-color`}
  ${tw`sm:flex-[0_0_30%] sm:border-r-0`}
  & > span {
    ${tw`text-body-color py-[7.5px] text-2xsmall`}
    &.active {
      ${tw`text-body-color font-normal`}
    }
  }
`
export const PISearchResult = styled.div`
  ${tw`relative bg-white z-[1] md:w-full`}
  transition: margin 0.2s linear 0s; 
  ${props => props.isInfoHidden ? tw`ml-[-48%]` : tw``}
  ${props => props.isOtherAlu ? tw`` : tw`border-0 border-solid border-l border-l-drawer-menu-color`}
`
export const PIHideInfoBtn = styled.div`
  ${tw`absolute top-0 left-[-23px] h-[160px] pt-[45px] px-[3px] pb-0 bg-hide-btn-color rounded-tr-[10px] rounded-br-[10px]
    text-white text-2xsmall font-museo-sans uppercase cursor-pointer`}
  ${tw`xxl:left-0 xxl:rounded-tl-[10px] xxl:rounded-tr-[0px] xxl:rounded-br-[0px] xxl:rounded-bl-[10px]`}
  ${tw`lg:hidden`}
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(-180deg);
  letter-spacing: 1.2px;
  user-select: none;
  & > svg {
    ${tw`absolute top-[23px] left-[2px]`}
  }
`
export const PISearchResultText = styled(SearchScreenPopularText)`
  ${tw`mb-[30px]`}
  span {
    ${tw`text-black`}
  }
  & > div {
    ${tw`text-paragraph-color`}
  }
`
export const PIResultTableWrapper = styled.div`
  ${tw`max-h-[585px] overflow-y-auto`}
  & > table {
    ${tw`md:w-[800px]`}
  }
`
export const PIHorizontalBlock = styled.div`
  ${tw`mb-[70px] `}
`
export const PIHorizontalBlockHeading = styled(HeadingTag)`
  ${tw`inline-block border-0 border-b border-solid border-b-body-color pr-[20px] pb-[10px] mt-[20px] mb-[10px]`}
`
export const PIProductName = styled.div``
export const PIProductDetails = styled.div`
  ${tw`flex mb-[40px]`}
  ${tw`lg:flex-col`}
`
export const PIProductSpecs = styled.div`
  ${tw`w-[250px] mr-[30px] lg:mr-0`}
  ul {
    ${tw`mb-[30px] mt-[20px]`}
    li {
      ${tw`py-[10px] px-0`}
      span {
        ${tw`min-w-[80px] inline-block font-museo-sans text-body-color font-light text-small`}
      }
    }
  }
`
export const PIOtherAlImage = styled.div`
  ${tw`mt-[30px] mr-0 min-w-[300px] py-[10px] px-[30px]`}
  img {
    ${tw`max-h-[600px] w-auto max-w-[400px] lg:w-full`}
  }
`