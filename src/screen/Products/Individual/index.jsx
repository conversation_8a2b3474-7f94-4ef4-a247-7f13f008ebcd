import { useEffect, useMemo, useState } from "react";
import { useQuery } from "@apollo/client";
import { useLocation, useParams, useRouteMatch } from "react-router-dom";
import { toast } from "react-toastify";

import {
  PIC<PERSON>r,
  PIHideInfoBtn,
  PIHorizontalBlock,
  PIHorizontalBlockHeading,
  PILeftPane,
  PINav,
  PINavLink,
  PINavLinkImg,
  PIOtherAlImage,
  PIProduct,
  PIProductDescription,
  PIProductDetails,
  PIProductDimension,
  PIProductHeading,
  PIProductImage,
  PIProductName,
  PIProductSpecs,
  PIResultTableWrapper,
  PIRightPane,
  PISearchContainer,
  PISearchCriteria,
  PISearchCriteriaWrapper,
  PISearchInput,
  PISearchInputWrapper,
  PISearchResult,
  PISearchResultText,
  PIWrapper,
} from "./style";
import MainLayout from "../../../component/globals/Layout";
import Icon from "../../../partials/Icon";
import { ProductTable } from "../../../component/globals/Table";
import {
  OtherAluminiumPath,
  ProductIndividualCriteriaDefaultCol,
  ProductIndividualHideInfo,
  ProductIndividualSearchResult,
  ProductIndividualShowInfo,
} from "../../../constant";
import generalProduct from "../../../graphql/generalProduct.gql";
import subCategories from "../../../graphql/subCategories.gql";
import productsQuery from "../../../graphql/productsQuery.gql";
import Button from "../../../component/globals/Button";
import { addToCart } from "../../../state/state.global";
import { stripHtml } from "../../../util/stripHtml";
import Breadcrumbs from "../../../component/globals/Breadcrumbs/Breadcrumbs"; // Add this line

const ProductIndividualScreen = ({ category, ...props }) => {
  const { subCat, prodCat } = useParams();
  const { url, path } = useRouteMatch();
  const location = useLocation();
  const isOtherAlu = useMemo(
    () => OtherAluminiumPath.includes(prodCat),
    [prodCat]
  );
  const [isClient, setIsClient] = useState(false);

  const [isInfoHidden, setIsInfoHidden] = useState(false);
  const [selectedCriteria, setSelectedCriteria] = useState("section");
  const [searchValue, setSearchValue] = useState("");

  const [catInfo, setCatInfo] = useState();
  const [columnData, setColumnData] = useState();
  const [dataSource, setDataSource] = useState();
  const [defaultDataSource, setDefaultDataSource] = useState();

  const {
    data: navData,
    loading: navLoading,
    error: navError,
  } = useQuery(generalProduct, {
    variables: {
      id: prodCat,
      // id: "geometric"
    },
  });
  const {
    data: subCatData,
    loading: subCatLoading,
    error: subCatError,
  } = useQuery(subCategories, {
    variables: {
      id: subCat,
      // id: "equal-angle"
    },
    onCompleted: (data) => {
      const dataCat = data.categoriesProduct;
      const dataAcfField = dataCat.acfProductCategory;

      const searchCriteria = {};
      const tableHeader = [];

      const { cateInfo, dimensionValue } = createCatInfo(dataCat, dataAcfField);
      setCatInfo(cateInfo);
      const a = ProductIndividualCriteriaDefaultCol;
      const tableCol = ProductIndividualCriteriaDefaultCol.map(
        (colData, idx) => {
          if (colData.default) {
            if (subCat.includes("perforated")) return;
            if (subCat.includes("coil")) return;
            if (subCat.includes("aluminium") && colData.dataIndex === "section")
              return;
            return colData;
          }
          const tableColData = dimensionValue.find(
            (x) => x.key === colData.key
          );
          if (!tableColData) return null;

          const newColTitle = tableColData.defaultValue.trim()
            ? `${colData.title} (${tableColData.defaultValue})`
            : `${colData.title}`;
          return {
            ...colData,
            title: newColTitle,
          };
        }
      ).filter((x) => !!x);
      setColumnData(tableCol);
    },
  });
  const { loading: prodLoading, error: prodError } = useQuery(productsQuery, {
    variables: {
      terms: subCat,
      // terms: "equal-angle"
    },
    onCompleted: (data) => {
      if (!data) return;
      const dataNodes = data.products.nodes;
      const transformedData = dataNodes.map((prod, idx) => {
        Object.keys(prod.acfProductSettings).forEach((k) => {
          if (!prod.acfProductSettings[k]) {
            delete prod.acfProductSettings[k];
          }
          if (k === "__typename") {
            delete prod.acfProductSettings[k];
          }
        });
        return {
          ...prod.acfProductSettings,
          section: prod.title,
          key: prod.id,
        };
      });
      setDataSource(transformedData);
      setDefaultDataSource(transformedData);
    },
  });

  const createCatInfo = (dataCat, dataAcfField) => {
    const cateInfo = {};
    cateInfo["name"] = dataCat.name;
    cateInfo["description"] = dataCat.description;
    cateInfo["img"] = {
      mediaItemUrl: dataAcfField.individualImg?.mediaItemUrl,
      altText: dataAcfField.individualImg?.altText,
    };
    const cateField = Object.keys(dataAcfField.imageDefaultValue)
      .map((k, idx) => {
        const val = dataAcfField.imageDefaultValue[k];
        const data = {};
        if (val && k !== "__typename") {
          data[k] = {
            defaultValue: val,
          };
          return data;
        }
      })
      .filter((x) => !!x);
    const dimensionValue = Object.keys(dataAcfField.imageDimension)
      .map((k, idx) => {
        const objVal = dataAcfField.imageDimension[k];
        if (objVal["__typename"]) delete objVal["__typename"];
        const foundVal = cateField.find((x) => x[k]);
        if (!foundVal) return null;
        return { ...foundVal[k], ...objVal, key: k };
      })
      .filter((x) => !!x);
    cateInfo["dimension"] = dimensionValue;

    return { cateInfo, dimensionValue };
  };

  const handleFilter = () => {
    if (!selectedCriteria || !dataSource) return;
    if (!searchValue || searchValue.length < 1) {
      setDataSource(defaultDataSource);
    }
    const filteredData = defaultDataSource.filter((x) =>
      x[selectedCriteria].includes(searchValue)
    );
    setDataSource(filteredData);
  };

  const handleCriteriaChange = (criteria) => {
    if (!criteria) return;
    setSelectedCriteria(criteria);
    setSearchValue("");
  };

  const handleMouseEnter = (data) => {
    if (!data || !catInfo) return;
    const dimension = catInfo.dimension.map((dim, idx) => {
      let dimensionKey = dim.key;
      if (dimensionKey === "t1") {
        dimensionKey = "thickness";
      }
      if (dim.defaultValue.trim() === "") return { ...dim, value: null };
      return {
        ...dim,
        value: data[dimensionKey],
      };
    });
    setCatInfo((prevCatInfo) => ({ ...prevCatInfo, dimension }));
  };
  const handleMouseLeave = () => {
    if (!catInfo) return;
    const dimension = catInfo.dimension.map((dim, idx) => {
      return {
        ...dim,
        value: null,
      };
    });
    setCatInfo((prevCatInfo) => ({ ...prevCatInfo, dimension }));
  };

  const handleAddToCart = (prod, prodImg = "", prodSubCat = "") => {
    if (prod.productImage) {
      prodImg = prod.productImage.mediaItemUrl;
      delete prod.productImage;
    }
    const prodObj = {
      ...prod,
      prodImg,
      prodSubCat,
    };
    addToCart(prodObj);
    toast.success("Item added!");
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    handleFilter();
  }, [searchValue, selectedCriteria]);

  // Define breadcrumb items
  const breadcrumbItems = [
    { name: "Home", link: "/" },
    { name: "Products", link: "/products" },
  ];
  if (navData && navData.categoriesProduct) {
    breadcrumbItems.push({ name: navData.categoriesProduct.name, link: `/products/${prodCat}` });
  }
  if (catInfo) {
    breadcrumbItems.push({ name: catInfo.name, link: url });
  }

  return (
    <MainLayout>
      <Breadcrumbs items={breadcrumbItems} containerType="individual" />
      <PIContainer>
        <PINav>
          {!navLoading &&
            navData &&
            navData.categoriesProduct?.children?.nodes.map((navItem, idx) => (
              <PINavLink
                key={navItem.id}
                isActive={subCat === navItem.slug}
                to={`/products/${prodCat}/${navItem.slug}`}
              >
                <PINavLinkImg
                  src={navItem.acfProductCategory?.categoryimg?.mediaItemUrl}
                  alt={navItem.acfProductCategory?.categoryimg?.altText}
                  width={isClient ? (stripHtml(navItem.acfProductCategory?.categoryimg?.caption) || "") : ""}
                />
                <span>{navItem.name}</span>
              </PINavLink>
            ))}
        </PINav>
        <PIWrapper>
          <PIProduct>
            <PILeftPane>
              {!subCatLoading && catInfo && (
                <>
                  <PIProductHeading as="h2" color="body" font="2xl">
                    <span>{catInfo.name}</span>
                  </PIProductHeading>
                  {!isOtherAlu && (
                    <>
                      <PIProductImage>
                        <figure>
                          <img
                            src={catInfo.img?.mediaItemUrl}
                            alt={catInfo.img?.altText}
                          />
                        </figure>
                        {catInfo.dimension &&
                          catInfo.dimension.map((di, idx) => (
                            <PIProductDimension
                              key={di.key}
                              style={{ top: di.top, left: di.left }}
                            >
                              {di.value ?? di.defaultValue}
                            </PIProductDimension>
                          ))}
                      </PIProductImage>
                      <PIProductDescription isHidden={isInfoHidden}>
                        {catInfo.description}
                      </PIProductDescription>
                    </>
                  )}
                </>
              )}
            </PILeftPane>
            <PIRightPane>
              {!subCatLoading && columnData && dataSource && (
                <>
                  <PISearchContainer isOtherAlu={isOtherAlu}>
                    <PISearchInputWrapper>
                      <PISearchInput
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        type="text"
                        placeholder="Search"
                      />
                      <Icon icon="search" width="20" />
                    </PISearchInputWrapper>
                    <PISearchCriteriaWrapper>
                      {columnData.map((colVal, idx) => (
                        <PISearchCriteria
                          key={colVal.key}
                          onClick={() => handleCriteriaChange(colVal.key)}
                          className={
                            selectedCriteria === colVal.key ? "active" : ""
                          }
                        >
                          <span>{colVal.title}</span>
                        </PISearchCriteria>
                      ))}
                    </PISearchCriteriaWrapper>
                  </PISearchContainer>
                  {!isOtherAlu && (
                    <PISearchResult isInfoHidden={isInfoHidden}>
                      <PIHideInfoBtn
                        onClick={() => setIsInfoHidden(!isInfoHidden)}
                      >
                        <Icon
                          icon={
                            isInfoHidden
                              ? "white-left-caret"
                              : "white-right-caret"
                          }
                          width="16"
                          height="16"
                        />
                        {!isInfoHidden
                          ? ProductIndividualHideInfo
                          : ProductIndividualShowInfo}
                      </PIHideInfoBtn>
                      <PISearchResultText>
                        <span>{ProductIndividualSearchResult}</span>
                        <div></div>
                      </PISearchResultText>
                      <PIResultTableWrapper>
                        <ProductTable
                          onMouseEnter={handleMouseEnter}
                          onMouseLeave={handleMouseLeave}
                          onAddBtnClick={(prod) =>
                            handleAddToCart(
                              prod,
                              catInfo.img?.mediaItemUrl,
                              catInfo.name
                            )
                          }
                          columns={columnData}
                          dataSource={dataSource}
                        />
                      </PIResultTableWrapper>
                    </PISearchResult>
                  )}
                </>
              )}
            </PIRightPane>
          </PIProduct>
          {isOtherAlu && dataSource && (
            <PISearchResult isOtherAlu>
              {dataSource.map((ds) => (
                <PIHorizontalBlock key={ds.section}>
                  <PIProductName>
                    <PIHorizontalBlockHeading as="h2" font="lg">
                      Section no: {ds.section}
                    </PIHorizontalBlockHeading>
                  </PIProductName>
                  <PIProductDetails>
                    <PIProductSpecs>
                      <ul>
                        <li>
                          <span>Height:</span>
                          <span>{ds.height}</span>
                        </li>
                        <li>
                          <span>Width:</span>
                          <span>{ds.width}</span>
                        </li>
                        <li>
                          <span>Thickness:</span>
                          <span>{ds.thickness}</span>
                        </li>
                        <li>
                          <span>Weight:</span>
                          <span>{ds.weight}</span>
                        </li>
                      </ul>
                      <Button
                        variant="iconBg"
                        iconHeight="28"
                        iconWidth="28"
                        icon="plus"
                        onClick={() =>
                          handleAddToCart(
                            ds,
                            catInfo.img?.mediaItemUrl,
                            catInfo.name
                          )
                        }
                      >
                        add to enquiry
                      </Button>
                    </PIProductSpecs>
                    <PIOtherAlImage>
                      <img
                        src={ds.productImage?.mediaItemUrl}
                        alt={ds.productImage?.altText}
                      />
                    </PIOtherAlImage>
                  </PIProductDetails>
                </PIHorizontalBlock>
              ))}
            </PISearchResult>
          )}
        </PIWrapper>
      </PIContainer>
    </MainLayout>
  );
};

export default ProductIndividualScreen;
