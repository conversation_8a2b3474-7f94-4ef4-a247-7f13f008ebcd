import tw from "twin.macro";
import styled from "styled-components";
import { Link } from "react-router-dom";

import {HeadingTag} from "../../../partials/Heading/style.js";

export const PCContainer = styled.div`
    ${tw`flex relative container-p px-[50px] pb-[50px] min-h-[calc(100vh - 150px)]`}
    @media screen and (max-width: 950px){
        ${tw`pb-[110px]`}
    }
    @media screen and (max-width: 700px){
        ${tw`py-[20px] px-[25px] flex-col`}
    }
    @media screen and (max-width: 580px){
        ${tw`pb-[115px]`}
    }
    @media screen and (max-width: 529px){
        ${tw`pb-[160px]`}
    }
`
export const PCProductInfoCol = styled.div`
    ${tw`flex flex-col mr-[20px]`}
    @media screen and (max-width: 700px){
        ${tw`w-full mr-0 py-0 px-[20px]`}
    }
`
export const PCProductCatCol = styled.div`
    ${tw`flex w-full min-h-[300px] max-h-full pt-[50px] justify-start items-start`}
    @media screen and (max-width: 700px){
        ${tw`pt-[20px]`}
    }
    flex-flow: wrap;
`
export const PCOurProductsHeading = styled(HeadingTag)`
    ${tw`my-0 text-menu-item`}
    @media screen and (min-width: 701px){
      ${tw`absolute top-[115px] left-[20px] text-right`}
      -webkit-writing-mode: vertical-lr;
      writing-mode: vertical-lr;
      text-orientation: mixed;
      transform: rotate(-180deg);
    }
`
export const PCProductHeading = styled(HeadingTag)`
    ${tw`mt-[20px] mb-[10px]`}
    & > span {
      ${tw`font-sans`}
    }
`
export const PCProductImage = styled.figure`
    ${tw`m-0 mb-[30px]`}
    @media screen and (max-width: 700px){
        ${tw`hidden`}
    }
`
export const PCProductInfo = styled.div`
    ${tw`mb-[15px]`}
    p {
      ${tw`m-0 font-museo-sans text-2xsmall text-paragraph-color`}
    }
`
export const PCProductWrapper = styled.div`
    ${tw`h-[150px] w-1/3 md:w-1/2`}
`
export const PCProduct = styled(Link)`
    ${tw`w-[80%] h-[140px] my-0 mx-auto py-[15px] px-[5px] flex flex-col justify-center
      items-center text-center cursor-pointer no-underline text-paragraph-color`}
    @media screen and (max-width: 700px){
        ${tw`w-full`}
    }
    & > img {
      ${tw`max-h-[100px] mb-[10px]`}
      @media screen and (max-width: 700px){
        ${tw`max-h-[80px]`}
      }
    }
    & > span {
      ${tw`text-small`}
    }
`
