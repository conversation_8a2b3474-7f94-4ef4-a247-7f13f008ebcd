import styled from 'styled-components';
import tw from 'twin.macro';
import { Link as RouterLink } from 'react-router-dom';

export const BreadcrumbContainer = styled.nav`
  ${tw`flex items-center mb-6  text-small`}
  ${tw`container-p`} // Align with container padding
  color: ${props => props.isOnDark ? '#ffffff' : '#666666'};

  // For pages with custom container styles
  &.custom-container {
    ${tw`max-w-[1260px] mx-auto px-5`}
  }

  // For conveyor pages with black background
  &.conveyor-container {
    ${tw`max-w-[1340px] mx-auto px-5 pt-5`}
  }

  // For product category pages positioned outside flex container
  &.category-container {
    ${tw`pt-[50px] pb-0 sm:pt-[65px]`}
  }

  // For individual product pages positioned outside flex container
  &.individual-container {
    ${tw`max-w-[1260px] mx-auto px-5 pt-[50px] pb-0 sm:pt-[115px]`}
  }
`;

export const BreadcrumbItem = styled.div`
  ${tw`flex items-center`}
`;

export const BreadcrumbLink = styled(RouterLink)`
  color: ${props => props.isOnDark ? '#ffffff' : '#333'};
  ${tw`no-underline transition-colors duration-200`}

  &:hover {
    color: ${props => props.isOnDark ? '#cccccc' : '#0056b3'};
    ${tw`underline`}
  }
`;

export const BreadcrumbSeparator = styled.span`
  ${tw`mx-2`}
  color: ${props => props.isOnDark ? '#cccccc' : '#cccccc'};
`;