import React from 'react';
import { Bread<PERSON>rumbContainer, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator } from './style';

const Breadcrumbs = ({ items, isOnDark = false, containerType = 'default' }) => {
  if (!items || items.length === 0) {
    return null;
  }

  const getContainerClass = () => {
    switch (containerType) {
      case 'custom':
        return 'custom-container';
      case 'conveyor':
        return 'conveyor-container';
      case 'category':
        return 'category-container';
      default:
        return '';
    }
  };

  return (
    <BreadcrumbContainer isOnDark={isOnDark} className={getContainerClass()}>
      {items.map((item, index) => (
        <BreadcrumbItem key={index}>
          {index < items.length - 1 ? (
            <BreadcrumbLink to={item.link} isOnDark={isOnDark}>{item.name}</BreadcrumbLink>
          ) : (
            <span style={{ color: isOnDark ? '#ffffff' : '#333333' }}>{item.name}</span>
          )}
          {index < items.length - 1 && <BreadcrumbSeparator isOnDark={isOnDark}>/</BreadcrumbSeparator>}
        </BreadcrumbItem>
      ))}
    </BreadcrumbContainer>
  );
};

export default Breadcrumbs;